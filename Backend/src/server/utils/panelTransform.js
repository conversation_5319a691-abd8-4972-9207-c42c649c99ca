/**
 * Transforms time series data into a format suitable for panel display.
 * @param {*} data data to be transformed
 * @returns 
 */
export const transformTimeSeries = (data) => {
    if (!data || !Array.isArray(data)) {
        return {
            columnOptions: {},
            metadata: {
                columnNames: [],
                hasData: false
            }
        };
    }

    // Assuming first column is date/time and other columns are values
    const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);
    const rows = Array.isArray(data[0]) ? data.slice(1) : data;

    // Find datetime column - exact match only (case insensitive)
    const dateColumnIndex = headers.findIndex(h => h === 'DateTime' || h === 'datetime' || h === 'Datetime' || h === 'date' || h === 'Date' || h === '_time');

    if (dateColumnIndex < 0) {
        // No DateTime column found, return raw data for frontend to handle
        return {
            columnOptions: {},
            metadata: {
                columnNames: [],
                hasData: false
            }
        };
    }

    const columnOptions = {};
    const columnNames = [];

    // Process each numeric column individually
    for (let i = 0; i < headers.length; i++) {
        if (i !== dateColumnIndex) {
            const columnName = headers[i];

            // Extract and process column data
            const rawColumnData = rows.map(row => {
                const value = Array.isArray(row) ? row[i] : row[headers[i]];
                const numValue = parseFloat(value);
                return isNaN(numValue) ? null : numValue;
            });

            // Only include columns that have at least some valid numeric data
            const validDataCount = rawColumnData.filter(val => val !== null).length;
            if (validDataCount > 0) {
                // Create ECharts data format: [[x, y], [x, y], ...]
                // Downsample data using decimation if too many points (e.g., > 2000)
                let echartsData = rows.map((row, index) => {
                    const x = Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]];
                    const y = rawColumnData[index];
                    return y !== null ? [x, y] : null;
                }).filter(point => point !== null);

                const MAX_POINTS = 2000;
                if (echartsData.length > MAX_POINTS) {
                    const step = Math.ceil(echartsData.length / MAX_POINTS);
                    echartsData = echartsData.filter((_, idx) => idx % step === 0);
                }

                // Calculate y-axis range with padding for this specific column
                const validYValues = rawColumnData.filter(val => val !== null);
                let yAxisMin = 'dataMin';
                let yAxisMax = 'dataMax';

                if (validYValues.length > 0) {
                    const minY = Math.min(...validYValues);
                    const maxY = Math.max(...validYValues);
                    const range = maxY - minY;
                    let padding;

                    if (range === 0 || range < 0.000001) {
                        // Flat line case
                        padding = Math.max(1, Math.abs(maxY) * 0.5);
                    } else {
                        // Normal case
                        padding = range < 0.01 ? Math.max(0.5, range) : range * 0.2;
                    }

                    yAxisMin = Math.round((minY - padding) * 100) / 100;
                    yAxisMax = Math.round((maxY + padding) * 100) / 100;
                }

                // Create individual ECharts configuration for this column
                columnOptions[columnName] = {
                    animation: false,
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross'
                        },
                        formatter: function (params) {
                            let result = `${new Date(params[0].value[0]).toLocaleString()}<br/>`;
                            result += `${params[0].seriesName}: ${params[0].value[1]}<br/>`;
                            return result;
                        }
                    },
                    legend: {
                        data: [columnName],
                        top: 30,
                        type: 'scroll'
                    },
                    toolbox: {
                        show: true
                    },
                    xAxis: {
                        type: 'time',
                        boundaryGap: false,
                        axisLine: {
                            onZero: false
                        },
                        splitLine: {
                            show: false
                        }
                    },
                    yAxis: {
                        type: 'value',
                        min: yAxisMin,
                        max: yAxisMax,
                        scale: true,
                        splitArea: {
                            show: true
                        }
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            xAxisIndex: [0],
                            filterMode: 'filter'
                        },
                        {
                            type: 'slider',
                            xAxisIndex: [0],
                            filterMode: 'filter'
                        }
                    ],
                    brush: {
                        toolbox: ['rect', 'polygon', 'clear'],
                        xAxisIndex: 0
                    },
                    series: [{
                        name: columnName,
                        type: 'line',
                        data: echartsData,
                        smooth: false,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            width: 2,
                        },
                        emphasis: {
                            focus: 'series'
                        }
                    }],
                    hasData: true,
                    // Column-specific metadata
                    metadata: {
                        columnName: columnName,
                        dataPoints: echartsData.length,
                        yRange: { min: yAxisMin, max: yAxisMax },
                        dateRange: echartsData.length > 0 ? {
                            min: Math.min(...echartsData.map(d => new Date(d[0]).getTime())),
                            max: Math.max(...echartsData.map(d => new Date(d[0]).getTime()))
                        } : null
                    }
                };

                columnNames.push(columnName);
            }
        }
    }

    // Return individual column configurations
    return {
        columnOptions: columnOptions,
        metadata: {
            columnNames: columnNames,
            hasData: columnNames.length > 0,
            totalColumns: columnNames.length
        }
    };
}

/**
 * Transforms data into histogram format with Plotly configurations.
 * @param {*} data data to be transformed
 * @returns Object containing columnOptions with Plotly configurations
 */
export const transformHistogram = (data) => {

    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0
            }
        };
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        const columnOptions = {};
        let processedColumns = 0;

        // Process all numeric columns except DateTime columns
        const columnsToProcess = headers
            .map((name, index) => ({ index, name }))
            .filter(({ name }) => !name.toLowerCase().includes('datetime'));

        columnsToProcess.forEach(({ index, name }) => {

            try {
                // Extract values for this column
                const values = rows.map(row => {
                    const value = Array.isArray(row) ? row[index] : row[name];
                    return value !== undefined && value !== null ? value : null;
                });

                // Extract numeric values and filter positive values only
                const numericValues = values
                    .map(value => typeof value === 'string' ? parseFloat(value) : value)
                    .filter(value => value !== null && !isNaN(value) && value > 0); // Only positive values

                if (numericValues.length === 0) {
                    return;
                }

                // Calculate statistics
                const min = Math.min(...numericValues);
                const max = Math.max(...numericValues);
                const mean = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;

                // Calculate median
                const sortedValues = [...numericValues].sort((a, b) => a - b);
                const median = sortedValues.length % 2 === 0
                    ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
                    : sortedValues[Math.floor(sortedValues.length / 2)];

                // Calculate standard deviation
                const variance = numericValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / numericValues.length;
                const stdDev = Math.sqrt(variance);

                // Dynamic bin calculation
                const binCount = Math.min(50, Math.max(10, Math.ceil(Math.sqrt(numericValues.length))));
                const binWidth = (max - min) / binCount;


                // Handle edge case where all values are the same (min === max)
                if (binWidth === 0 || !isFinite(binWidth)) {

                    // Create a single bin for identical values
                    const plotlyData = [{
                        x: [min],
                        y: [numericValues.length],
                        type: 'bar',
                        name: name,
                        marker: {
                            color: 'rgba(55, 128, 191, 0.7)',
                            line: {
                                color: 'rgba(55, 128, 191, 1.0)',
                                width: 1
                            }
                        },
                        hovertemplate: '<b>%{fullData.name}</b><br>' +
                            'Value: %{x:.2f}<br>' +
                            'Count: %{y}<br>' +
                            '<extra></extra>'
                    }];

                    // Create Plotly layout for single value
                    const plotlyLayout = {
                        title: {
                            text: `Histogram: ${name}`,
                            font: { size: 16 }
                        },
                        xaxis: {
                            title: name,
                            showgrid: true,
                            zeroline: false,
                            range: [min - 1, min + 1] // Add some padding around the single value
                        },
                        yaxis: {
                            title: 'Frequency',
                            showgrid: true,
                            zeroline: false
                        },
                        bargap: 0.1,
                        showlegend: false,
                        margin: { l: 60, r: 30, t: 60, b: 60 },
                        plot_bgcolor: 'rgba(0,0,0,0)',
                        paper_bgcolor: 'rgba(0,0,0,0)'
                    };

                    // Store complete Plotly configuration for single value case
                    columnOptions[name] = {
                        plotlyData: plotlyData,
                        plotlyLayout: plotlyLayout,
                        statistics: {
                            mean: mean.toFixed(2),
                            median: median.toFixed(2),
                            min: min.toFixed(2),
                            max: max.toFixed(2),
                            stdDev: stdDev.toFixed(2),
                            count: numericValues.length,
                            binCount: 1
                        },
                        metadata: {
                            columnName: name,
                            dataPoints: numericValues.length,
                            originalDataPoints: values.length,
                            positiveValuesOnly: true,
                            singleValue: true
                        }
                    };

                    processedColumns++;
                    return; // Skip the normal binning process
                }

                // Create bins for normal case
                const bins = Array.from({ length: binCount }, (_, i) => ({
                    start: min + i * binWidth,
                    end: min + (i + 1) * binWidth,
                    count: 0
                }));

                // Count values in each bin with proper bounds checking
                numericValues.forEach((value, valueIndex) => {
                    try {
                        let binIndex = Math.floor((value - min) / binWidth);

                        // Ensure binIndex is within valid range
                        binIndex = Math.max(0, Math.min(binCount - 1, binIndex));

                        // Additional safety check
                        if (bins && bins[binIndex] && typeof bins[binIndex].count === 'number') {
                            bins[binIndex].count++;
                        } else {
                            console.warn(`Backend - transformHistogram: Invalid bin access - binIndex: ${binIndex}, bins length: ${bins?.length}, bin exists: ${!!bins[binIndex]}, value: ${value}, column: ${name}`);
                        }
                    } catch (binError) {
                        console.error(`Backend - transformHistogram: Error processing value ${value} at index ${valueIndex} for column ${name}:`, binError);
                    }
                });

                // Create Plotly data with safety checks
                const plotlyData = [{
                    x: bins.map(bin => bin ? (bin.start + bin.end) / 2 : 0), // Bin centers with safety check
                    y: bins.map(bin => bin ? bin.count : 0), // Bin counts with safety check
                    type: 'bar',
                    name: name,
                    marker: {
                        color: 'rgba(55, 128, 191, 0.7)',
                        line: {
                            color: 'rgba(55, 128, 191, 1.0)',
                            width: 1
                        }
                    },
                    hovertemplate: '<b>%{fullData.name}</b><br>' +
                        'Range: %{x:.2f}<br>' +
                        'Count: %{y}<br>' +
                        '<extra></extra>'
                }];

                // Create Plotly layout
                const plotlyLayout = {
                    title: {
                        text: `Histogram: ${name}`,
                        font: { size: 16 }
                    },
                    xaxis: {
                        title: name,
                        showgrid: true,
                        zeroline: false
                    },
                    yaxis: {
                        title: 'Frequency',
                        showgrid: true,
                        zeroline: false
                    },
                    bargap: 0.1,
                    showlegend: false,
                    margin: { l: 60, r: 30, t: 60, b: 60 },
                    plot_bgcolor: 'rgba(0,0,0,0)',
                    paper_bgcolor: 'rgba(0,0,0,0)'
                };

                // Store complete Plotly configuration
                columnOptions[name] = {
                    plotlyData: plotlyData,
                    plotlyLayout: plotlyLayout,
                    statistics: {
                        mean: mean.toFixed(2),
                        median: median.toFixed(2),
                        min: min.toFixed(2),
                        max: max.toFixed(2),
                        stdDev: stdDev.toFixed(2),
                        count: numericValues.length,
                        binCount: binCount
                    },
                    metadata: {
                        columnName: name,
                        dataPoints: numericValues.length,
                        originalDataPoints: values.length,
                        positiveValuesOnly: true
                    }
                };

                processedColumns++;
            } catch (columnError) {
                console.error(`Backend - transformHistogram: Error processing column ${name}:`, columnError);
                // Continue with next column instead of failing entire operation
            }
        });

        const result = {
            columnOptions: columnOptions,
            metadata: {
                hasData: Object.keys(columnOptions).length > 0,
                totalColumns: headers.length,
                processedColumns: processedColumns,
                excludedColumns: headers.length - processedColumns,
                processedRows: rows.length
            }
        };

        return result;

    } catch (error) {
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: error.message
            }
        };
    }
};

/**
 * Transforms data into overview format with column statistics.
 * @param {*} data data to be transformed
 * @returns Object containing columnStatistics array with pre-calculated statistics
 */
export const transformOverview = (data) => {

    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            columnStatistics: [],
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0
            }
        };
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        const columnStatistics = [];
        let processedColumns = 0;

        // Process all columns except DateTime columns (used for filtering and x-axis)
        const columnsToProcess = headers
            .map((name, index) => ({ index, name }))
            .filter(({ name }) => !name.toLowerCase().includes('datetime'));

        columnsToProcess.forEach(({ index, name }) => {
            // Extract values for this column
            const values = rows.map(row => {
                const value = Array.isArray(row) ? row[index] : row[name];
                return value !== undefined && value !== null ? value : null;
            });

            // Extract numeric values for calculations
            const numericValues = values
                .map(value => typeof value === 'string' ? parseFloat(value) : value)
                .filter(value => value !== null && !isNaN(value));

            // Count missing values
            const missingCount = values.filter(value =>
                value === null || value === undefined || value === ''
            ).length;

            // Count distinct values
            const distinctCount = new Set(
                values.filter(value =>
                    value !== null && value !== undefined && value !== ''
                )
            ).size;

            // Calculate statistics
            let mean = 'N/A';
            let min = 'N/A';
            let max = 'N/A';
            let stdDev = 'N/A';

            if (numericValues.length > 0) {
                // Calculate mean
                const sum = numericValues.reduce((acc, val) => acc + val, 0);
                const meanValue = sum / numericValues.length;
                mean = meanValue.toFixed(2);

                // Calculate min and max
                min = Math.min(...numericValues).toFixed(2);
                max = Math.max(...numericValues).toFixed(2);

                // Calculate standard deviation
                const squaredDifferences = numericValues.map(value => Math.pow(value - meanValue, 2));
                const variance = squaredDifferences.reduce((acc, val) => acc + val, 0) / numericValues.length;
                stdDev = Math.sqrt(variance).toFixed(2);
            }

            columnStatistics.push({
                name,
                mean,
                min,
                max,
                stdDev,
                missingValues: missingCount.toString(),
                distinctValues: distinctCount.toString(),
                // Additional metadata for potential future use
                totalValues: values.length,
                numericValues: numericValues.length,
                dataType: numericValues.length > 0 ? 'numeric' : 'categorical'
            });

            processedColumns++;
        });

        const result = {
            columnStatistics,
            metadata: {
                hasData: columnStatistics.length > 0,
                totalColumns: headers.length,
                processedColumns: processedColumns,
                excludedColumns: headers.length - processedColumns,
                processedRows: rows.length
            }
        };

        return result;

    } catch (error) {
        return {
            columnStatistics: [],
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: error.message
            }
        };
    }
};

/**
 * Transforms data into scatter plot format with ECharts configurations.
 * Creates scatter plots using target variable as X-axis and other columns as Y-axis.
 * @param {*} data data to be transformed
 * @param {*} payload optional payload containing filters and target variable configuration
 * @returns Object containing columnOptions with ECharts configurations
 */
export const transformScatterPlot = (data, payload = null) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0
            }
        };
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        // Get target variable from configuration
        const targetVariable = payload?.configuration?.targetVariable;

        // Check if target variable is provided
        if (!targetVariable) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: "No target variable selected. Please configure a target variable to plot scatter plot."
                }
            };
        }

        // Find target variable column (case-insensitive search)
        const targetColumnIndex = headers.findIndex(h => h.toLowerCase() === targetVariable.toLowerCase());
       
        if (targetColumnIndex < 0) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: `Target variable '${targetVariable}' not found in CSV file`
                }
            };
        }

        const columnOptions = {};
        let processedColumns = 0;

        // Process all numeric columns except the target variable
        headers.forEach((columnName, columnIndex) => {
            if (columnIndex === targetColumnIndex || columnName === targetVariable) return;

            try {
                // Extract values for this column
                const values = rows.map(row => {
                    const targetValue = Array.isArray(row) ? row[targetColumnIndex] : row[targetVariable];
                    const columnValue = Array.isArray(row) ? row[columnIndex] : row[columnName];
                    return {
                        target: targetValue,
                        value: columnValue !== undefined && columnValue !== null ? columnValue : null
                    };
                });

                // Filter out null/undefined values and convert to numbers
                const validData = values
                    .filter(item => item.target && item.value !== null && item.value !== undefined)
                    .map(item => {
                        const targetNum = typeof item.target === 'string' ? parseFloat(item.target) : item.target;
                        const numValue = typeof item.value === 'string' ? parseFloat(item.value) : item.value;
                        return { target: targetNum, value: numValue };
                    })
                    .filter(item => !isNaN(item.target) && !isNaN(item.value));

                if (validData.length === 0) return;

                // Prepare data points for ECharts scatter plot [targetValue, columnValue]
                const scatterData = validData.map(item => [item.target, item.value]);

                // Calculate axis ranges with proper rounding
                const xValues = validData.map(item => item.target);
                const yValues = validData.map(item => item.value);
                const xMin = Math.min(...xValues);
                const xMax = Math.max(...xValues);
                const yMin = Math.min(...yValues);
                const yMax = Math.max(...yValues);
                const xRange = xMax - xMin;
                const yRange = yMax - yMin;
                const xPadding = xRange * 0.1;
                const yPadding = yRange * 0.1;

                // Round the axis limits to avoid long decimal numbers
                const xAxisMin = Math.round((xMin - xPadding) * 100) / 100;
                const xAxisMax = Math.round((xMax + xPadding) * 100) / 100;
                const yAxisMin = Math.round((yMin - yPadding) * 100) / 100;
                const yAxisMax = Math.round((yMax + yPadding) * 100) / 100;

                // Create ECharts configuration for this column's scatter plot
                columnOptions[columnName] = {
                    animation: false,
                    tooltip: {
                        trigger: 'item',
                        formatter: function (params) {
                            return `${targetVariable}: ${params.value[0]}<br/>${columnName}: ${params.value[1]}`;
                        }
                    },
                    legend: {
                        data: [columnName],
                        top: 30,
                        type: 'scroll'
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'value',
                        name: targetVariable,
                        nameLocation: 'middle',
                        nameGap: 30,
                        min: xAxisMin,
                        max: xAxisMax,
                        axisLabel: {
                            formatter: function (value) {
                                return parseFloat(value.toFixed(2));
                            }
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                type: 'dashed',
                                color: '#E5E5E5'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: columnName,
                        nameLocation: 'middle',
                        nameGap: 50,
                        min: yAxisMin,
                        max: yAxisMax,
                        scale: true,
                        splitLine: {
                            show: true,
                            lineStyle: {
                                type: 'dashed',
                                color: '#E5E5E5'
                            }
                        },
                        axisLabel: {
                            formatter: function (value) {
                                // Format Y-axis values to 2 decimal places maximum
                                const num = Number(value);
                                if (num === 0) return '0';
                                if (Number.isInteger(num)) return num.toString();
                                return num.toFixed(2);
                            }
                        }
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            xAxisIndex: [0],
                            filterMode: 'filter'
                        },
                        {
                            type: 'slider',
                            xAxisIndex: [0],
                            filterMode: 'filter',
                            bottom: '5%'
                        }
                    ],
                    series: [{
                        name: columnName,
                        type: 'scatter',
                        data: scatterData,
                        symbolSize: 8,
                        itemStyle: {
                            color: '#4682B4', // Using color from your existing scatter plot
                            opacity: 0.8
                        },
                        emphasis: {
                            itemStyle: {
                                color: '#FFD700', // Golden color for emphasis
                                opacity: 1,
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.3)'
                            }
                        }
                    }],
                    hasData: true,
                    metadata: {
                        columnName: columnName,
                        targetVariable: targetVariable,
                        dataPoints: scatterData.length,
                        xRange: { min: xMin, max: xMax },
                        yRange: { min: yMin, max: yMax }
                    }
                };

                processedColumns++;
            } catch (columnError) {
                console.error(`Backend - transformScatterPlot: Error processing column ${columnName}:`, columnError);
            }
        });

        return {
            columnOptions: columnOptions,
            metadata: {
                hasData: Object.keys(columnOptions).length > 0,
                totalColumns: headers.length - 1, // Exclude target variable column
                processedColumns: processedColumns,
                targetVariable: targetVariable
            }
        };

    } catch (error) {
        console.error('Backend - transformScatterPlot: Error processing data:', error);
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: error.message
            }
        };
    }
};

/**
 * Transforms data into X̄-R control chart format with ECharts configurations.
 * @param {*} data data to be transformed
 * @param {*} payload optional payload containing filters and other parameters
 * @returns Object containing columnOptions with ECharts configurations
 */
export const transformXbarRbar = (data, payload = null) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0
            }
        };
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        const columnOptions = {};
        let processedColumns = 0;

        // Process all numeric columns except DateTime columns
        const numericColumns = headers
            .map((name, index) => ({ index, name }))
            .filter(({ name }) => !name.toLowerCase().includes('datetime'));

        numericColumns.forEach(({ index, name }) => {
            try {
                // Extract values for this column
                const values = rows.map(row => {
                    const value = Array.isArray(row) ? row[index] : row[name];
                    return value !== undefined && value !== null ? value : null;
                });

                // Extract numeric values
                const numericValues = values
                    .map(value => typeof value === 'string' ? parseFloat(value) : value)
                    .filter(value => value !== null && !isNaN(value));

                if (numericValues.length < 10) return; // Need at least 10 points for control charts

                // Group data into samples using configurable subgroup size
                const sampleSize = payload?.configuration?.subgroupSize || 5;
                const samples = [];
                for (let i = 0; i < numericValues.length; i += sampleSize) {
                    const sample = numericValues.slice(i, i + sampleSize);
                    if (sample.length === sampleSize) {
                        samples.push(sample);
                    }
                }

                if (samples.length < 2) return; // Need at least 2 samples

                // Calculate sample means (X̄) and ranges (R)
                const sampleMeans = samples.map(sample =>
                    sample.reduce((sum, val) => sum + val, 0) / sample.length
                );
                const sampleRanges = samples.map(sample =>
                    Math.max(...sample) - Math.min(...sample)
                );

                // Calculate control limits
                const grandMean = sampleMeans.reduce((sum, mean) => sum + mean, 0) / sampleMeans.length;
                const averageRange = sampleRanges.reduce((sum, range) => sum + range, 0) / sampleRanges.length;

                // Control chart constants for sample size 5
                const A2 = 0.577; // For X̄ chart
                const D3 = 0; // For R chart lower limit
                const D4 = 2.114; // For R chart upper limit

                const xbarUCL = grandMean + (A2 * averageRange);
                const xbarLCL = grandMean - (A2 * averageRange);
                const rUCL = D4 * averageRange;
                const rLCL = D3 * averageRange;

                // Create sample indices for x-axis
                const sampleIndices = samples.map((_, index) => index + 1);

                // Create ECharts configuration for X̄-R charts
                columnOptions[name] = {
                    animation: false,
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            let result = `Sample ${params[0].axisValue}<br/>`;
                            params.forEach(param => {
                                result += `${param.seriesName}: ${param.value.toFixed(3)}<br/>`;
                            });
                            return result;
                        }
                    },
                    legend: [
                        {
                            data: ['X̄ (Sample Mean)', 'X̄ UCL', 'X̄ LCL', 'X̄ Center Line'],
                            top: 10,
                            left: 'center',
                            textStyle: { fontSize: 12 }
                        },
                        {
                            data: ['R (Range)', 'R UCL', 'R Center Line'],
                            top: '55%',
                            left: 'center',
                            textStyle: { fontSize: 12 }
                        }
                    ],
                    grid: [
                        {
                            left: '10%',
                            right: '10%',
                            top: '15%',
                            height: '35%',
                            containLabel: true
                        },
                        {
                            left: '10%',
                            right: '10%',
                            top: '60%',
                            height: '35%',
                            containLabel: true
                        }
                    ],
                    xAxis: [
                        {
                            type: 'category',
                            data: sampleIndices,
                            gridIndex: 0
                        },
                        {
                            type: 'category',
                            data: sampleIndices,
                            gridIndex: 1
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            gridIndex: 0,
                            min: Math.round(Math.min(xbarLCL, ...sampleMeans) * 0.95 * 100) / 100,
                            max: Math.round(Math.max(xbarUCL, ...sampleMeans) * 1.05 * 100) / 100,
                            axisLabel: {
                                formatter: function (value) {
                                    // Format Y-axis values to 2 decimal places maximum
                                    const num = Number(value);
                                    if (num === 0) return '0';
                                    if (Number.isInteger(num)) return num.toString();
                                    return num.toFixed(2);
                                }
                            }
                        },
                        {
                            type: 'value',
                            gridIndex: 1,
                            min: 0,
                            max: Math.round(rUCL * 1.1 * 100) / 100,
                            axisLabel: {
                                formatter: function (value) {
                                    // Format Y-axis values to 2 decimal places maximum
                                    const num = Number(value);
                                    if (num === 0) return '0';
                                    if (Number.isInteger(num)) return num.toString();
                                    return num.toFixed(2);
                                }
                            }
                        }
                    ],
                    series: (() => {
                        // Create data with custom styling for out-of-control points
                        const xbarData = sampleMeans.map((value, index) => {
                            const isOutOfControl = value > xbarUCL || value < xbarLCL;
                            return {
                                value: [index, value],
                                itemStyle: isOutOfControl ? { color: '#ff0000' } : { color: '#1f77b4' }
                            };
                        });

                        const rData = sampleRanges.map((value, index) => {
                            const isOutOfControl = value > rUCL || value < rLCL;
                            return {
                                value: [index, value],
                                itemStyle: isOutOfControl ? { color: '#ff0000' } : { color: '#ff7f0e' }
                            };
                        });

                        return [
                            // X̄ Chart - Connected line with individual point styling
                            {
                                name: 'X̄ (Sample Mean)',
                                type: 'line',
                                data: xbarData,
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                symbol: 'circle',
                                symbolSize: 6,
                                lineStyle: { width: 2, color: '#1f77b4' },
                                emphasis: {
                                    focus: 'series'
                                }
                            },
                            // X̄ Chart Control Lines
                            {
                                name: 'X̄ UCL',
                                type: 'line',
                                data: new Array(sampleMeans.length).fill(xbarUCL),
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                lineStyle: { type: 'dashed', color: '#d62728', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#d62728' }
                            },
                            {
                                name: 'X̄ LCL',
                                type: 'line',
                                data: new Array(sampleMeans.length).fill(xbarLCL),
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                lineStyle: { type: 'dashed', color: '#d62728', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#d62728' }
                            },
                            {
                                name: 'X̄ Center Line',
                                type: 'line',
                                data: new Array(sampleMeans.length).fill(grandMean),
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                lineStyle: { type: 'solid', color: '#2ca02c', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#2ca02c' }
                            },
                            // R Chart - Connected line with individual point styling
                            {
                                name: 'R (Range)',
                                type: 'line',
                                data: rData,
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                symbol: 'circle',
                                symbolSize: 6,
                                lineStyle: { width: 2, color: '#ff7f0e' },
                                emphasis: {
                                    focus: 'series'
                                }
                            },
                            // R Chart Control Lines
                            {
                                name: 'R UCL',
                                type: 'line',
                                data: new Array(sampleRanges.length).fill(rUCL),
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                lineStyle: { type: 'dashed', color: '#d62728', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#d62728' }
                            },
                            {
                                name: 'R LCL',
                                type: 'line',
                                data: new Array(sampleRanges.length).fill(rLCL),
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                lineStyle: { type: 'dashed', color: '#d62728', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#d62728' }
                            },
                            {
                                name: 'R Center Line',
                                type: 'line',
                                data: new Array(sampleRanges.length).fill(averageRange),
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                lineStyle: { type: 'solid', color: '#2ca02c', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#2ca02c' }
                            }
                        ];
                    })(),
                    hasData: true,
                    metadata: {
                        columnName: name,
                        sampleCount: samples.length,
                        sampleSize: sampleSize,
                        grandMean: grandMean,
                        averageRange: averageRange,
                        controlLimits: {
                            xbar: { ucl: xbarUCL, lcl: xbarLCL, center: grandMean },
                            r: { ucl: rUCL, lcl: rLCL, center: averageRange }
                        }
                    }
                };

                processedColumns++;
            } catch (columnError) {
                console.error(`Backend - transformXbarRbar: Error processing column ${name}:`, columnError);
            }
        });

        return {
            columnOptions: columnOptions,
            metadata: {
                hasData: Object.keys(columnOptions).length > 0,
                totalColumns: numericColumns.length,
                processedColumns: processedColumns
            }
        };

    } catch (error) {
        console.error('Backend - transformXbarRbar: Error processing data:', error);
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: error.message
            }
        };
    }
};

/**
 * Transforms panel data based on the type of panel.
 * @param {*} data data to be transformed
 * @param {*} type type of panel (e.g., 'TimeSeriesPanel', 'OverviewPanel', 'HistogramPanel')
 * @param {*} payload optional payload containing filters and other parameters
 * @returns
 */
export const transformPanelData = (data, type, payload = null) => {
    if (!data || !Array.isArray(data)) {
        return [];
    }

    switch (type) {
        case 'TimeSeriesPanel':
            const transformedData = transformTimeSeries(data);
            return transformedData;
        case 'HistogramPanel':
            return transformHistogram(data);
        case 'OverviewPanel':
            return transformOverview(data);
        case 'DataTablePanel':
            return data;
        case 'ScatterPlotPanel':
            return transformScatterPlot(data, payload);
        case 'XbarRbarPanel':
            return transformXbarRbar(data, payload);
        default:
            return data;
    }
}



/**
 * Filters the panel data based on the provided filters.
 *
 * @param {Array} results - The array of panel data to be filtered.
 * @param {Object} filters - The filters to apply to the panel data.
 * @returns {Array} The filtered array of panel data. Returns an empty array if results is not a valid array.
 */
export const filterPanelData = (results, filters) => {
    if (!results || !Array.isArray(results)) {
        return [];
    }

    // Filter based on conditional filters
    if (filters['conditionalFilters'] && Array.isArray(filters['conditionalFilters']) && filters['conditionalFilters'].length > 0) {
        return filterByConditionalFilters(results, filters['conditionalFilters']);
    }

    // Filter based on date range if provided
    if (filters['dateFilter'] && filters['dateFilter']['startDate'] && filters['dateFilter']['endDate']) {
        return filterByDateRange(results, filters['dateFilter']['startDate'], filters['dateFilter']['endDate']);
    }

    // Filter based on annotation filters if provided
    if (filters['annotationFilters'] && Array.isArray(filters['annotationFilters']) && filters['annotationFilters'].length > 0) {
        return filterByAnnotations(results, filters['annotationFilters']);
    }

    // Filter based on operation filters if provided
    if (filters['operationFilters'] && Array.isArray(filters['operationFilters']) && filters['operationFilters'].length > 0) {
        return filterByOperations(results, filters['operationFilters']);
    }

    // If no specific panel type is requested, return all results
    return results;
}

/**
 * Filters the panel data based on the provided filters.
 *
 * @param {Array} results - The array of panel data to be filtered.
 * @param {Object} filters - The filters to apply to the panel data.
 * @returns {Array} The filtered array of panel data. Returns an empty array if results is not a valid array.
 */
export const filterPanelDataNew = (results, filters) => {
    if (!results || !Array.isArray(results)) {
        return [];
    }

    // Parse headers and rows
    const headers = Array.isArray(results[0]) ? results[0].map(String) : Object.keys(results[0]);
    const rows = Array.isArray(results[0]) ? results.slice(1) : results;

    // Prepare filter conditions
    const dateColumnIndex = headers.findIndex(h => h === 'DateTime' || h === 'datetime' || h === 'Datetime' || h === 'date' || h === 'Date' || h === '_time');
    const startTimestamp = filters.dateFilter?.startDate ? new Date(filters.dateFilter.startDate).getTime() : null;
    const endTimestamp = filters.dateFilter?.endDate ? new Date(filters.dateFilter.endDate).getTime() : null;

    // Build a single filter function
    const shouldInclude = (row) => {
        // Date filter
        if (dateColumnIndex >= 0 && startTimestamp && endTimestamp) {
            const dateValue = Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]];
            const dateTimestamp = new Date(dateValue).getTime();
            if (isNaN(dateTimestamp) || dateTimestamp < startTimestamp || dateTimestamp > endTimestamp) return false;
        }

        // Annotation filters
        if (filters.annotationFilters?.length) {
            for (const annotation of filters.annotationFilters) {
                const { columnName, x0, x1, y0, y1 } = annotation;
                const colIdx = headers.indexOf(columnName);
                if (colIdx < 0) continue;
                const xValue = Array.isArray(row) ? row[colIdx] : row[columnName];
                const xTimestamp = new Date(xValue).getTime();
                const featureValue = Array.isArray(row) ? row[colIdx + 1] : row[headers[colIdx + 1]];
                if (isNaN(xTimestamp) || xTimestamp < x0 || xTimestamp > x1 || featureValue < y0 || featureValue > y1) return false;
            }
        }

        // Operation filters
        if (filters.operationFilters?.length) {
            for (const operation of filters.operationFilters) {
                const { columnName, y0, y1 } = operation;
                const colIdx = headers.indexOf(columnName);
                if (colIdx < 0) continue;
                const featureValue = Array.isArray(row) ? row[colIdx + 1] : row[headers[colIdx + 1]];
                if (featureValue < y0 || featureValue > y1) return false;
            }
        }

        return true;
    };

    // Filter rows in a single pass
    const filteredRows = rows.filter(shouldInclude);

    // Return in the same format as input
    return filteredRows.length > 0 && Array.isArray(results[0])
        ? [headers, ...filteredRows]
        : filteredRows;
}

/**
 * Filters the data by a specified date range.
 *
 * @param {Array} data - The data to filter.
 * @param {Date} startDate - The start date of the range.
 * @param {Date} endDate - The end date of the range.
 * @returns {Array} Filtered data within the specified date range.
 */
export const filterByDateRange = (data, startDate, endDate) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        const dateColumnIndex = headers.findIndex(h => h.toLowerCase() === 'datetime');
        if (dateColumnIndex < 0) {
            console.warn('No DateTime column found for filtering by date range.');
            return data; // No filtering possible, return original data
        }

        // Convert start and end dates to timestamps if not already
        const startTimestamp = new Date(startDate).getTime();
        const endTimestamp = new Date(endDate).getTime();

        if (isNaN(startTimestamp) || isNaN(endTimestamp)) {
            console.error('Invalid date range provided for filtering:', startDate, endDate);
            return []; // Invalid date range, return empty array
        }

        // Filter rows based on the date range
        const filteredRows = rows.filter(row => {
            const dateValue = Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]];
            const dateTimestamp = new Date(dateValue).getTime();
            return dateTimestamp >= startTimestamp && dateTimestamp <= endTimestamp;
        });

        // Return headers with filtered rows or empty array if no data matches
        return filteredRows.length > 0 ? [headers, ...filteredRows] : [];

    } catch (error) {
        console.error('Error filtering data by date range:', error);
        return [];
    }
}

export const filterByConditionalFilters = (data, conditionalFilters) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
    }

    try {
        conditionalFilters.forEach(filter => {
            if (
                filter.enabled &&
                filter.type === 'conditional-column' &&
                filter.column &&
                filter.operator === 'by_value' &&
                Array.isArray(filter.value)
            ) {
                data = data.filter(row => {
                    // Support both object and array row formats
                    if (typeof row === 'object' && !Array.isArray(row)) {
                        // Object format
                        return filter.value.includes(String(row[filter.column]));
                    } else if (Array.isArray(row)) {
                        // Array format: need to find column index
                        const headers = data[0] && Array.isArray(data[0]) ? data[0] : [];
                        const colIdx = headers.findIndex(h => h === filter.column);
                        if (colIdx === -1) return false;
                        return filter.value.includes(String(row[colIdx]));
                    }
                    return false;
                });
            }
        });
    }
    catch(error) {
        console.error('Error filtering data by conditional filters:', error);
        return [];
    }
}

export const filterByAnnotations = (data, annotations) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
    }

    try {
        annotations.forEach(annotation => {
            const { columnName, x0, x1, y0, y1 } = annotation;
            const columnIndex = data[0].indexOf(columnName);

            // Check if x0 and x1 are valid timestamps within the data
            if (columnIndex < 0 || isNaN(x0) || isNaN(x1) || x0 >= x1) {
                console.warn(`Invalid annotation for column ${columnName}:`, annotation);
                return; // Skip invalid annotations
            }

            // Filter rows based on the annotation's x0 and x1 values
            data = data.filter(row => {
                const xValue = Array.isArray(row) ? row[columnIndex] : row[columnName];
                const xTimestamp = new Date(xValue).getTime();
                const featureValue = Array.isArray(row) ? row[columnIndex + 1] : row[headers[columnIndex + 1]];
                return xTimestamp >= x0 && xTimestamp <= x1 && featureValue >= y0 && featureValue <= y1;
            });
        })
    } catch (error) {
        console.error('Error filtering data by annotations:', error);
        return [];
    }
}

export const filterByOperations = (data, operations) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
    }

    try {
        operations.forEach(operation => {
            const { columnName, y0, y1 } = operation;
            const columnIndex = data[0].indexOf(columnName);

            // Check if x0 and x1 are valid timestamps within the data
            if (columnIndex < 0 || isNaN(x0) || isNaN(x1) || x0 >= x1) {
                console.warn(`Invalid operation for column ${columnName}:`, operation);
                return; // Skip invalid operations
            }

            // Filter rows based on the operation's x0 and x1 values
            data = data.filter(row => {
                const featureValue = Array.isArray(row) ? row[columnIndex + 1] : row[headers[columnIndex + 1]];
                return featureValue >= y0 && featureValue <= y1;
            });
        })
    } catch (error) {
        console.error('Error filtering data by operations:', error);
        return [];
    }
}