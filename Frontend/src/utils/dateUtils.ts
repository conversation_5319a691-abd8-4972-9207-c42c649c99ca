/**
 * Utility functions for consistent date handling across the application
 */

/**
 * Converts a date value to a consistent ISO string format for backend communication
 * @param dateValue - The date value to convert (string, number, or Date)
 * @param isEndDate - Whether this is an end date (adds end of day time for date-only strings)
 * @returns ISO string or null if no date provided
 */
export const toBackendDateFormat = (dateValue: string | number | Date | null, isEndDate: boolean = false): string | null => {
  if (!dateValue) return null;

  try {
    let date: Date;

    if (typeof dateValue === 'number') {
      // Timestamp
      date = new Date(dateValue);
    } else if (typeof dateValue === 'string') {
      if (dateValue.includes('T')) {
        // Already in ISO format with time
        return dateValue;
      } else {
        // Date-only string, add appropriate time
        const timeString = isEndDate ? 'T23:59:59.999Z' : 'T00:00:00.000Z';
        date = new Date(dateValue + timeString);
      }
    } else {
      // Date object
      date = new Date(dateValue);
    }

    // Validate the date
    if (isNaN(date.getTime())) {
      console.warn('Invalid date provided to toBackendDateFormat:', dateValue);
      return null;
    }

    return date.toISOString();
  } catch (error) {
    console.error('Error converting date to backend format:', error, dateValue);
    return null;
  }
};

/**
 * Processes a date filter object for backend communication
 * @param dateFilter - Object with startDate and endDate properties
 * @returns Processed date filter with consistent ISO format
 */
export const processDateFilterForBackend = (dateFilter: { startDate: any; endDate: any }) => {
  return {
    startDate: toBackendDateFormat(dateFilter.startDate, false),
    endDate: toBackendDateFormat(dateFilter.endDate, true)
  };
};

/**
 * Converts a date from user's local timezone to UTC for consistent backend processing
 * Useful when you want to preserve the exact time the user selected
 * @param dateValue - The date value in user's local timezone
 * @returns ISO string in UTC
 */
export const toUTCDateFormat = (dateValue: string | number | Date | null): string | null => {
  if (!dateValue) return null;

  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      console.warn('Invalid date provided to toUTCDateFormat:', dateValue);
      return null;
    }
    return date.toISOString();
  } catch (error) {
    console.error('Error converting date to UTC format:', error, dateValue);
    return null;
  }
};

/**
 * Converts a date from graph selection to a format that preserves the user's intended local time
 * This is used when the user selects a date range from a chart and we want to preserve
 * the exact date/time they selected, not convert it to UTC
 * @param dateValue - The date value from graph selection
 * @returns Date string in local timezone format that can be consistently processed
 */
export const fromGraphSelectionToLocalDate = (dateValue: string | number | Date | null): string | null => {
  if (!dateValue) return null;

  try {
    console.log('fromGraphSelectionToLocalDate - Input:', dateValue, 'Type:', typeof dateValue);

    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      console.warn('Invalid date provided to fromGraphSelectionToLocalDate:', dateValue);
      return null;
    }

    console.log('fromGraphSelectionToLocalDate - Parsed date object:', date);
    console.log('fromGraphSelectionToLocalDate - Date in local timezone:', date.toString());

    // Format as YYYY-MM-DD HH:mm:ss in local timezone
    // This preserves the user's intended time without timezone conversion
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    const result = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    console.log('fromGraphSelectionToLocalDate - Result:', result);

    return result;
  } catch (error) {
    console.error('Error converting graph selection to local date:', error, dateValue);
    return null;
  }
};

/**
 * Enhanced backend date format converter that handles local datetime strings
 * @param dateValue - The date value to convert (string, number, or Date)
 * @param isEndDate - Whether this is an end date (adds end of day time for date-only strings)
 * @returns ISO string or null if no date provided
 */
export const toBackendDateFormatEnhanced = (dateValue: string | number | Date | null, isEndDate: boolean = false): string | null => {
  if (!dateValue) return null;

  try {
    console.log('toBackendDateFormatEnhanced - Input:', dateValue, 'Type:', typeof dateValue, 'isEndDate:', isEndDate);

    let date: Date;

    if (typeof dateValue === 'number') {
      // Timestamp
      console.log('toBackendDateFormatEnhanced - Processing as timestamp');
      date = new Date(dateValue);
    } else if (typeof dateValue === 'string') {
      if (dateValue.includes('T')) {
        // Already in ISO format with time
        console.log('toBackendDateFormatEnhanced - Already in ISO format, returning as-is');
        return dateValue;
      } else if (dateValue.includes(' ')) {
        // Local datetime format like "2025-05-04 22:38:00"
        // Parse as local time and convert to UTC
        console.log('toBackendDateFormatEnhanced - Processing as local datetime string');
        date = new Date(dateValue);
      } else {
        // Date-only string, add appropriate time
        console.log('toBackendDateFormatEnhanced - Processing as date-only string');
        const timeString = isEndDate ? 'T23:59:59.999Z' : 'T00:00:00.000Z';
        date = new Date(dateValue + timeString);
      }
    } else {
      // Date object
      console.log('toBackendDateFormatEnhanced - Processing as Date object');
      date = new Date(dateValue);
    }

    console.log('toBackendDateFormatEnhanced - Parsed date object:', date);
    console.log('toBackendDateFormatEnhanced - Date in local timezone:', date.toString());

    // Validate the date
    if (isNaN(date.getTime())) {
      console.warn('Invalid date provided to toBackendDateFormatEnhanced:', dateValue);
      return null;
    }

    const result = date.toISOString();
    console.log('toBackendDateFormatEnhanced - Final ISO result:', result);

    return result;
  } catch (error) {
    console.error('Error converting date to backend format:', error, dateValue);
    return null;
  }
};

/**
 * Enhanced date filter processor that handles graph selections properly
 * @param dateFilter - Object with startDate and endDate properties
 * @returns Processed date filter with consistent ISO format
 */
export const processDateFilterForBackendEnhanced = (dateFilter: { startDate: any; endDate: any }) => {
  return {
    startDate: toBackendDateFormatEnhanced(dateFilter.startDate, false),
    endDate: toBackendDateFormatEnhanced(dateFilter.endDate, true)
  };
};

/**
 * Formats a date for display purposes
 * @param dateValue - The date value to format
 * @param format - The desired format (default: 'YYYY-MM-DD')
 * @returns Formatted date string
 */
export const formatDateForDisplay = (dateValue: string | number | Date | null, format: string = 'YYYY-MM-DD'): string => {
  if (!dateValue) return '';

  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      return '';
    }

    // Simple formatting for common cases
    if (format === 'YYYY-MM-DD') {
      return date.toISOString().split('T')[0];
    } else if (format === 'MM/DD/YYYY') {
      return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}/${date.getFullYear()}`;
    } else if (format === 'DD-MM-YYYY') {
      return `${date.getDate().toString().padStart(2, '0')}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getFullYear()}`;
    }

    // Default fallback
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting date for display:', error, dateValue);
    return '';
  }
};
