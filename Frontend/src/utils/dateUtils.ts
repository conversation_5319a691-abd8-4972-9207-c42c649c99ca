/**
 * Utility functions for consistent date handling across the application
 */

/**
 * Converts a date value to a consistent ISO string format for backend communication
 * @param dateValue - The date value to convert (string, number, or Date)
 * @param isEndDate - Whether this is an end date (adds end of day time for date-only strings)
 * @returns ISO string or null if no date provided
 */
export const toBackendDateFormat = (dateValue: string | number | Date | null, isEndDate: boolean = false): string | null => {
  if (!dateValue) return null;

  try {
    let date: Date;

    if (typeof dateValue === 'number') {
      // Timestamp
      date = new Date(dateValue);
    } else if (typeof dateValue === 'string') {
      if (dateValue.includes('T')) {
        // Already in ISO format with time
        return dateValue;
      } else {
        // Date-only string, add appropriate time
        const timeString = isEndDate ? 'T23:59:59.999Z' : 'T00:00:00.000Z';
        date = new Date(dateValue + timeString);
      }
    } else {
      // Date object
      date = new Date(dateValue);
    }

    // Validate the date
    if (isNaN(date.getTime())) {
      console.warn('Invalid date provided to toBackendDateFormat:', dateValue);
      return null;
    }

    return date.toISOString();
  } catch (error) {
    console.error('Error converting date to backend format:', error, dateValue);
    return null;
  }
};

/**
 * Processes a date filter object for backend communication
 * @param dateFilter - Object with startDate and endDate properties
 * @returns Processed date filter with consistent ISO format
 */
export const processDateFilterForBackend = (dateFilter: { startDate: any; endDate: any }) => {
  return {
    startDate: toBackendDateFormat(dateFilter.startDate, false),
    endDate: toBackendDateFormat(dateFilter.endDate, true)
  };
};

/**
 * Converts a date from user's local timezone to UTC for consistent backend processing
 * Useful when you want to preserve the exact time the user selected
 * @param dateValue - The date value in user's local timezone
 * @returns ISO string in UTC
 */
export const toUTCDateFormat = (dateValue: string | number | Date | null): string | null => {
  if (!dateValue) return null;

  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      console.warn('Invalid date provided to toUTCDateFormat:', dateValue);
      return null;
    }
    return date.toISOString();
  } catch (error) {
    console.error('Error converting date to UTC format:', error, dateValue);
    return null;
  }
};

/**
 * Formats a date for display purposes
 * @param dateValue - The date value to format
 * @param format - The desired format (default: 'YYYY-MM-DD')
 * @returns Formatted date string
 */
export const formatDateForDisplay = (dateValue: string | number | Date | null, format: string = 'YYYY-MM-DD'): string => {
  if (!dateValue) return '';

  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      return '';
    }

    // Simple formatting for common cases
    if (format === 'YYYY-MM-DD') {
      return date.toISOString().split('T')[0];
    } else if (format === 'MM/DD/YYYY') {
      return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}/${date.getFullYear()}`;
    } else if (format === 'DD-MM-YYYY') {
      return `${date.getDate().toString().padStart(2, '0')}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getFullYear()}`;
    }

    // Default fallback
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting date for display:', error, dateValue);
    return '';
  }
};
