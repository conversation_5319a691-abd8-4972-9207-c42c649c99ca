import React, { useEffect, useRef } from "react";
import { ColumnSelection, DateFilter } from "../../types";
import { PanelFilter, createConditionalColumnFilter } from "../../FilterTypes";
import { Spin } from "antd";
import Handsontable from "handsontable";
import "handsontable/dist/handsontable.full.css";
import "../../DateFilterPanel";
import DateFilterPanel from "../../DateFilterPanel";
import { useSelector } from "react-redux";

interface DataTablePanelProps {
  data: any; // Original complete raw data
  filteredData?: any; // Data after applying all filters (column, date, and table plugin filters)
  // Note: We don't use filteredData for rendering the table
  // Instead, we use data and apply only column and date filters
  // This allows Handsontable to maintain its own filtering state
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  conditionalFilters?: PanelFilter[];

  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (
    startDate: string | null,
    endDate: string | null
  ) => void;

  // New prop for handling conditional filters
  onAddFilter?: (filter: PanelFilter) => void;
  onRemoveFilter?: (filterId: string) => void;
}

const DataTablePanel: React.FC<DataTablePanelProps> = ({
  data,
  filteredData, // Not used in this component, but included for consistency with other panels
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  panelFilters = [],
  conditionalFilters = [],
  // onColumnSelection,
  onDateFilterChange,
  onAddFilter,
  onRemoveFilter,
}) => {
  const tableRef = useRef<HTMLDivElement>(null);
  const hotRef = useRef<Handsontable | null>(null);
  const originalDataRef = useRef<any[]>([]);
  const headersRef = useRef<string[]>([]);

  const annotationsState = useSelector((state: any) => state.annotations);
  const operationsState = useSelector((state: any) => state.operations);

  // Store the current filter conditions so we can reapply them when data changes
  // This is necessary because Handsontable loses its filter state when the data changes
  // (e.g., when date range or column selection filters are applied from other panels)
  const filterConditionsRef = useRef<Record<string, any>>({});

  // This component uses the following approach:
  // 1. Always use the original data from the 'data' prop
  // 2. Apply date range filter if provided
  // 3. Apply column selection filter to show only selected columns
  // 4. Let Handsontable handle its own filtering
  // This ensures that Handsontable always has access to the dataset after column and date filters
  // but before its own plugin filters, so clear filters button works correctly

  useEffect(() => {
    if (tableRef.current && data && !isLoading) {
      if (hotRef.current) {
        hotRef.current.destroy();
      }

      // Extract headers and data
      let headers: string[] = [];
      let tableData: any[][] = [];

      if (Array.isArray(data) && data.length > 0) {
        // Assuming first row contains headers
        headers = Array.isArray(data[0])
          ? data[0].map(String)
          : Object.keys(data[0]);
        tableData = Array.isArray(data[0])
          ? data.slice(1)
          : data.map((row: any) => Object.values(row));
      }

      // Store original data for reference
      originalDataRef.current = tableData;

      // Store headers for filtering
      const originalHeaders = headers;

      // Always use the original data from the data prop
      // This ensures that Handsontable has access to the complete dataset
      console.log('tableData', tableData)
      let processedData = tableData;

      // Apply date range filter if provided
      if (dateFilter.startDate && dateFilter.endDate) {
        console.log("Applying date filter:", dateFilter);

        // Find the date column (usually first column or named 'DateTime')
        const dateColumnIndex = headers.findIndex(
          (header) => header.toLowerCase() === "datetime"
        );

        if (dateColumnIndex >= 0) {
          console.log('dateFilter', dateFilter)
          const startDate = new Date(dateFilter.startDate);
          console.log('startDate', startDate)
          const endDate = new Date(dateFilter.endDate);

          console.log('processedData', processedData)
          // processedData = processedData.filter((row: any) => {
          //   const dateValue = row[dateColumnIndex];
          //   console.log('dateValue', dateValue)
          //   if (!dateValue) return false;

          //   const rowDate = new Date(dateValue);
          //   console.log('rowDate', rowDate)
          //   return rowDate >= startDate && rowDate <= endDate;
          // });

          console.log("Data after date filter:", processedData.length);
        }
      }

      // Apply annotation filters from the annotationsState
      if (annotationsState && annotationsState.annotations) {
        // Find annotations that have applyAsFilter set to true
        const annotationsToFilter = annotationsState.annotations.flatMap(
          (group: any) =>
            group.annotations
              .filter((annotation: any) => annotation.applyAsFilter)
              .map((annotation: any) => ({
                ...annotation,
                columnName:
                  group.columnName || annotationsState.activeColumnName,
              }))
        );

        if (annotationsToFilter.length > 0) {
          console.log(
            "Applying annotation filters to processedData:",
            annotationsToFilter
          );

          // Find date column for x-axis filtering
          const dateColumnIndex = headers.findIndex(
            (header) => header.toLowerCase() === "datetime"
          );

          // Apply each annotation filter to processedData (not filteredData)
          annotationsToFilter.forEach((annotation: any) => {
            const columnName = annotation.columnName;

            // Find the column index for the y-axis values
            const columnIndex = headers.findIndex(
              (header) => header === columnName
            );

            if (dateColumnIndex >= 0 && columnIndex >= 0) {
              // Convert annotation boundaries to proper types
              const x0Date = new Date(annotation.x0);
              const x1Date = new Date(annotation.x1);
              const y0Value = annotation.y0;
              const y1Value = annotation.y1;

              // Get min and max y values (in case y0 > y1)
              const minY = Math.min(y0Value, y1Value);
              const maxY = Math.max(y0Value, y1Value);

              console.log(
                `Filtering data for column ${columnName} with annotation range: ${minY} - ${maxY}`
              );

              // Filter out data points that fall within the annotation boundaries
              processedData = processedData.filter((row: any) => {
                const dateValue = row[dateColumnIndex];
                const yValue = parseFloat(row[columnIndex]);

                if (!dateValue || isNaN(yValue)) return true; // Keep rows with invalid data

                const rowDate = new Date(dateValue);

                // Check if the point falls within the annotation boundaries
                const isInXRange = rowDate >= x0Date && rowDate <= x1Date;
                const isInYRange = yValue >= minY && yValue <= maxY;

                // Return true to keep points that are OUTSIDE the annotation area
                return !(isInXRange && isInYRange);
              });
            }
          });

          console.log("Data after annotation filters:", processedData.length);
        }
      }

      // Apply operation filters from the operationsState
      if (operationsState && operationsState.operations) {
        // Find operations that have applyAsFilter set to true
        const operationsToFilter = operationsState.operations.flatMap(
          (group: any) =>
            group.operations
              .filter((operation: any) => operation.applyAsFilter)
              .map((operation: any) => ({
                ...operation,
                columnName:
                  group.columnName || operationsState.activeColumnName,
              }))
        );

        if (operationsToFilter.length > 0) {
          console.log(
            "Applying operation filters to processedData:",
            operationsToFilter
          );

          // Group operations by column name
          const operationsByColumn: Record<string, any[]> = {};
          operationsToFilter.forEach((operation: any) => {
            const columnName = operation.columnName;
            if (!operationsByColumn[columnName]) {
              operationsByColumn[columnName] = [];
            }
            operationsByColumn[columnName].push(operation);
          });

          // Apply operation filters for each column separately
          Object.entries(operationsByColumn).forEach(
            ([columnName, operations]) => {
              // Find the column index for the y-axis values
              const columnIndex = headers.findIndex(
                (header) => header === columnName
              );

              if (columnIndex >= 0) {
                // Apply each operation filter for this column
                operations.forEach((operation: any) => {
                  // Get the operation boundaries
                  const y0Value = operation.y0;
                  const y1Value = operation.y1;

                  // Get min and max values (in case y0 > y1)
                  const minY = Math.min(y0Value, y1Value);
                  const maxY = Math.max(y0Value, y1Value);

                  console.log(
                    `Filtering data for column ${columnName} with operation range: ${minY} - ${maxY}`
                  );

                  // Filter data points - KEEP only points that are WITHIN the operation range
                  // This is the opposite of annotation filtering
                  processedData = processedData.filter((row: any) => {
                    const yValue = parseFloat(row[columnIndex]);

                    if (isNaN(yValue)) return false; // Remove rows with invalid data

                    // Check if the point falls within the operation boundaries
                    const isInYRange = yValue >= minY && yValue <= maxY;

                    // Return true to keep points that are INSIDE the operation range
                    return isInYRange;
                  });
                });
              }
            }
          );

          console.log("Data after operation filters:", processedData.length);
        }
      }

      // Apply column selection if provided
      let visibleColumns = headers;
      console.log('processedData', processedData)
      let visibleData = processedData;

      if (selectedColumns.indices.length > 0) {
        let selectedIndices = [...selectedColumns.indices];

        // Sort indices to maintain original column order
        selectedIndices.sort((a, b) => a - b);

        // Filter headers to only include selected columns
        visibleColumns = selectedIndices.map((idx) => headers[idx]);

        // Filter original data to only include selected columns
        visibleData = processedData.map((row: any[]) =>
          selectedIndices.map((colIdx) => row[colIdx])
        );
      }

      // Use the original data for Handsontable
      const dataWithCheckboxes = visibleData;
      const headersWithCheckbox = visibleColumns;

      // Update headersRef to match the visible columns for filter operations
      headersRef.current = headersWithCheckbox;

      // Update headersRef to match the visible columns for filter operations
      headersRef.current = headersWithCheckbox;

      // Determine column types based on data
      const columnTypes = headersWithCheckbox.map((header, index) => {
        // Default to text type
        // let type = 'text';


        // Check if it's a date/time column
        // if (header.toLowerCase().includes('date') || header.toLowerCase().includes('time')) {
        //   return { type: 'date', dateFormat: 'YYYY-MM-DD HH:mm:ss', correctFormat: true, readOnly: true };
        // }


        // Check if it's a numeric column by examining data
        if (dataWithCheckboxes.length > 0) {
          // Check first few rows to determine if column is numeric
          const sampleSize = Math.min(10, dataWithCheckboxes.length);
          let numericCount = 0;


          for (let i = 0; i < sampleSize; i++) {
            const row = dataWithCheckboxes[i];
            if (row && row[index] !== null && row[index] !== undefined) {
              // If it's already a number or can be parsed as a number
              if (
                typeof row[index] === "number" ||
                (typeof row[index] === "string" && !isNaN(Number(row[index])))
              ) {
                numericCount++;
              }
            }
          }


          // If most sample values are numeric, treat as numeric column
          if (numericCount > sampleSize * 0.7) {
            return { type: "numeric", readOnly: true };
          }
        }

        return { type: "text", readOnly: true };
      });

      // Important: We're intentionally NOT using the filteredData prop here
      // Instead, we're using the original data with only column and date filters applied
      // This allows Handsontable to maintain its own filtering state and the clear filter button works correctly
      // The filteredData prop is used by other panels, but not by this one

      // Initialize Handsontable with the original data
      console.log('dataWithCheckboxes', dataWithCheckboxes)
      hotRef.current = new Handsontable(tableRef.current, {
        data: dataWithCheckboxes,
        colHeaders: headersWithCheckbox,
        rowHeaders: true,
        height: "100%",
        width: "100%",
        licenseKey: "non-commercial-and-evaluation",
        stretchH: "all",
        contextMenu: false, // Disable right-click menu
        manualColumnResize: false, // Disable column resizing
        manualRowResize: false, // Disable row resizing
        manualColumnMove: false, // Disable column moving
        manualRowMove: false, // Disable row moving
        className: "htDark",
        disableVisualSelection: true, // Disable visual selection
        fragmentSelection: false, // Disable fragment selection
        columns: columnTypes,

        // Enable filters with enhanced operators
        filters: true,

        dropdownMenu: {
          items: {
            filter_by_condition: {
              name: "Filter by condition",
            },
            filter_by_value: {
              name: "Filter by value",
            },
            filter_action_bar: {
              name: "Filter actions",
            },
            separator: "---------",
            clear_filter: {
              name: "Clear filter",
              callback: function (this: any) {
                const hot = this;
                const filtersPlugin = hot.getPlugin("filters");
                filtersPlugin.clearConditions();
                filtersPlugin.filter();
                if (onRemoveFilter) {
                  onRemoveFilter("");
                }
              },
            },
          },
        },

        // Filter callback - Capture filter conditions and convert to conditional filters
        afterFilter: function () {
          if (hotRef.current && onAddFilter) {
            const hot = hotRef.current;
            const filtersPlugin = hot.getPlugin("filters");
            // Get all active filter conditions
            if (filtersPlugin) {
              try {
                // Access the filters directly from the plugin
                // @ts-ignore - Accessing internal API
                let columnFilters: Record<string, any> = {};

                // Try to access filters using the correct property path
                if (filtersPlugin && filtersPlugin.conditionCollection) {
                  // Try to use getConditions method if available
                  if (
                    typeof filtersPlugin.conditionCollection.getConditions ===
                    "function"
                  ) {
                    try {
                      // Get all column indexes
                      const columnIndexes = [];
                      const colCount = hot.countCols();
                      for (let i = 0; i < colCount; i++) {
                        columnIndexes.push(i);
                      }

                      // Try to get conditions for each column
                      columnIndexes.forEach((columnIndex) => {
                        try {
                          const conditions =
                            filtersPlugin.conditionCollection.getConditions(
                              columnIndex
                            );
                          if (conditions && conditions.length > 0) {
                            columnFilters[columnIndex.toString()] = conditions;
                          }
                        } catch (e) {
                          console.error(
                            `Error getting conditions for column ${columnIndex}:`,
                            e
                          );
                        }
                      });
                    } catch (e) {
                      console.error("Error getting conditions:", e);
                    }
                  } else {
                    // Fallback: getConditions method not available
                    console.warn(
                      "getConditions method not available on ConditionCollection, skipping direct condition access"
                    );
                  }
                } else {
                  // Try to use the filtersPlugin directly
                  try {
                    // Get all column indexes
                    const columnIndexes = [];
                    const colCount = hot.countCols();
                    for (let i = 0; i < colCount; i++) {
                      columnIndexes.push(i);
                    }

                    // Check if any columns are filtered
                    columnIndexes.forEach((columnIndex) => {
                      // @ts-ignore - Using internal API
                      if (filtersPlugin.isColumnFiltered(columnIndex)) {
                        // Create a dummy condition for this column
                        columnFilters[columnIndex.toString()] = [
                          {
                            name: "filtered",
                            args: ["true"],
                          },
                        ];
                      }
                    });
                  } catch (e) {
                    console.error("Error checking filtered columns:", e);
                  }
                }

                // Save the filter conditions for later reapplication
                filterConditionsRef.current = columnFilters;

                // Clear existing conditional filters first
                if (onRemoveFilter) {
                  // Find and remove existing conditional filters
                  panelFilters.forEach((filter) => {
                    if (filter.type === "conditional-column") {
                      onRemoveFilter(filter.id);
                    }
                  });
                }

                // Convert Handsontable filters to our conditional filters
                Object.entries(columnFilters).forEach(
                  ([columnIndex, conditions]) => {
                    const colIndex = parseInt(columnIndex);
                    const columnName = headersRef.current[colIndex];

                    // @ts-ignore - Accessing private property for demonstration
                    conditions.forEach((condition: any) => {
                      // Map Handsontable condition to our filter format
                      let operator: any;
                      let value: any;

                      switch (condition.name) {
                        case "eq":
                          operator = "=";
                          value = condition.args[0];
                          break;
                        case "neq":
                          operator = "!=";
                          value = condition.args[0];
                          break;
                        case "gt":
                          operator = ">";
                          value = condition.args[0];
                          break;
                        case "gte":
                          operator = ">=";
                          value = condition.args[0];
                          break;
                        case "lt":
                          operator = "<";
                          value = condition.args[0];
                          break;
                        case "lte":
                          operator = "<=";
                          value = condition.args[0];
                          break;
                        case "between":
                          operator = "between";
                          // Ensure both values are properly formatted
                          if (condition.args && condition.args.length >= 2) {
                            // For numeric columns, ensure values are numbers for internal processing
                            // but convert to strings for the filter API which expects string values
                            const colIndex = parseInt(columnIndex);
                            const columnType = columnTypes[colIndex]?.type;

                            if (columnType === "numeric") {
                              // Convert to numbers for calculation but store as strings for the filter API
                              const val1 =
                                typeof condition.args[0] === "string"
                                  ? Number(condition.args[0])
                                  : condition.args[0];
                              const val2 =
                                typeof condition.args[1] === "string"
                                  ? Number(condition.args[1])
                                  : condition.args[1];

                              // Store as strings for the filter API
                              value = [String(val1), String(val2)];
                            } else {
                              // For non-numeric columns, ensure values are strings
                              value = [
                                String(condition.args[0]),
                                String(condition.args[1]),
                              ];
                            }
                          } else {
                            value = condition.args
                              ? condition.args.map(String)
                              : [];
                          }
                          break;
                        case "not_between":
                          operator = "not_between";
                          // Similar handling as 'between'
                          if (condition.args && condition.args.length >= 2) {
                            const colIndex = parseInt(columnIndex);
                            const columnType = columnTypes[colIndex]?.type;

                            if (columnType === "numeric") {
                              // Convert to numbers for calculation but store as strings for the filter API
                              const val1 =
                                typeof condition.args[0] === "string"
                                  ? Number(condition.args[0])
                                  : condition.args[0];
                              const val2 =
                                typeof condition.args[1] === "string"
                                  ? Number(condition.args[1])
                                  : condition.args[1];

                              // Store as strings for the filter API
                              value = [String(val1), String(val2)];
                            } else {
                              // For non-numeric columns, ensure values are strings
                              value = [
                                String(condition.args[0]),
                                String(condition.args[1]),
                              ];
                            }
                          } else {
                            value = condition.args
                              ? condition.args.map(String)
                              : [];
                          }
                          break;
                        case "contains":
                          operator = "contains";
                          value = condition.args[0];
                          break;
                        case "begins_with":
                          operator = "starts-with";
                          value = condition.args[0];
                          break;
                        case "ends_with":
                          operator = "ends-with";
                          value = condition.args[0];
                          break;
                        case "filtered":
                          // This is our dummy condition for when we can't access the real conditions
                          operator = "=";
                          value = "filtered";
                          break;
                        case "by_value":
                          operator = "by_value";
                          // Ensure we capture all values from the args array
                          value = condition.args;
                          // If args is a single string with commas, it might need to be split
                          if (
                            condition.args.length === 1 &&
                            typeof condition.args[0] === "string" &&
                            condition.args[0].includes(",")
                          ) {
                            value = condition.args[0]
                              .split(",")
                              .map((val) => val.trim());
                          }
                          // Ensure value is a flat array
                          if (Array.isArray(value)) {
                            value = value.flat();
                          }
                          break;
                        default:
                          return; // Skip unsupported conditions
                      }

                      // Create a unique ID for the filter
                      const filterId = `conditional-filter-${columnName}-${operator}-${Date.now()}`;

                      // Create and add the conditional filter
                      const filter = createConditionalColumnFilter(
                        filterId,
                        columnName,
                        operator,
                        value
                      );

                      // This will directly call handleConditionalFilter in WorkFlowContainer
                      onAddFilter(filter);
                    });
                  }
                );
              } catch (error) {
                console.error("Error processing Handsontable filters:", error);
              }
            }

            // Get the physical row indices that are visible after filtering
            const visibleRows = [];
            const totalRows = hot.countRows();

            if (totalRows > 0) {
              for (let i = 0; i < totalRows; i++) {
                if (!hot.rowIndexMapper.isHidden(i)) {
                  visibleRows.push(i);
                }
              }
            }
            if (totalRows > 0) {
              for (let i = 0; i < totalRows; i++) {
                if (!hot.rowIndexMapper.isHidden(i)) {
                  visibleRows.push(i);
                }
              }
            }
          }
        },

        // Disable selection handling
        afterSelectionEnd: () => {
          // Do nothing - selection is disabled
        },
        // Only highlight columns from external selections (not from clicking)
        cells: function (_row, col) {
          const cellProperties: { className?: string } = {};

          // Only highlight columns selected from Overview panel
          if (selectedColumns.indices.includes(col)) {
            cellProperties.className = "selected-column";
          }

          return cellProperties;
        },
      });

      // Apply conditional filters if they exist
      // This ensures that Handsontable's filters are applied when the view is loaded
      if (
        hotRef.current &&
        conditionalFilters &&
        conditionalFilters.length > 0
      ) {
        const filtersPlugin = hotRef.current.getPlugin("filters");

        // Clear any existing filters first to avoid conflicts
        filtersPlugin.clearConditions();

        // Check for incompatible filters and remove them from global state
        const incompatibleFilters: string[] = [];
        conditionalFilters.forEach((filter) => {
          if (filter.type === "conditional-column") {
            const columnFilter = filter as any;
            const column = columnFilter.column;
            const isColumnVisible = headersRef.current.includes(column);
            if (!isColumnVisible) {
              incompatibleFilters.push(filter.id);
            }
          }
        });

        // Remove incompatible filters from global state
        if (incompatibleFilters.length > 0 && onRemoveFilter) {
          incompatibleFilters.forEach((filterId) => {
            onRemoveFilter(filterId);
          });
        }

        // Process each conditional filter (only compatible ones will remain)
        const compatibleFilters = conditionalFilters.filter((filter) => {
          if (filter.type === "conditional-column") {
            const columnFilter = filter as any;
            return headersRef.current.includes(columnFilter.column);
          }
          return true;
        });

        compatibleFilters.forEach((filter) => {
          if (filter.type === "conditional-column") {
            const columnFilter = filter as any; // Type assertion
            const column = columnFilter.column;
            const operator = columnFilter.operator;
            const value = columnFilter.value;

            // Find the column index
            const columnIndex = headersRef.current.findIndex(
              (header) => header === column
            );
            if (columnIndex >= 0) {
              // Convert operator to Handsontable condition name
              let conditionName: string;
              let args: any[] = [value];

              switch (operator) {
                case "=":
                  conditionName = "eq";
                  break;
                case "!=":
                  conditionName = "neq";
                  break;
                case ">":
                  conditionName = "gt";
                  break;
                case ">=":
                  conditionName = "gte";
                  break;
                case "<":
                  conditionName = "lt";
                  break;
                case "<=":
                  conditionName = "lte";
                  break;
                case "between":
                  conditionName = "between";
                  // Handle different formats of filter values for between
                  if (Array.isArray(value) && value.length >= 2) {
                    args = [value[0], value[1]];
                  } else if (typeof value === "string" && value.includes(",")) {
                    // If value is a comma-separated string, split it into an array
                    const parts = value.split(",").map((val) => val.trim());
                    args = [parts[0], parts[1]];
                  } else {
                    console.warn("Invalid between filter value format:", value);
                    args = [0, 0]; // Default values
                  }
                  break;
                case "not_between":
                  conditionName = "not_between";
                  // Similar handling as 'between'
                  if (Array.isArray(value) && value.length >= 2) {
                    args = [value[0], value[1]];
                  } else if (typeof value === "string" && value.includes(",")) {
                    const parts = value.split(",").map((val) => val.trim());
                    args = [parts[0], parts[1]];
                  } else {
                    console.warn(
                      "Invalid not_between filter value format:",
                      value
                    );
                    args = [0, 0]; // Default values
                  }
                  break;
                case "contains":
                  conditionName = "contains";
                  break;
                case "starts-with":
                  conditionName = "begins_with";
                  break;
                case "ends-with":
                  conditionName = "ends_with";
                  break;
                case "by_value":
                  conditionName = "by_value";

                  // Handle different formats of filter values
                  if (typeof value === "string" && value.includes(",")) {
                    // If value is a comma-separated string, split it into an array
                    args = value.split(",").map((val) => val.trim());
                  } else if (Array.isArray(value)) {
                    // If value is an array, flatten it in case it contains nested arrays
                    args = value.flat();
                  } else {
                    // Otherwise, wrap single value in an array
                    args = [value];
                    // Otherwise, wrap single value in an array
                    args = [value];
                  }
                  break;
                default:
                  return;
              }
              try {
                if (conditionName === "by_value") {
                  const valueArray = Array.isArray(args) ? args : [args];
                  filtersPlugin.addCondition(columnIndex, conditionName, [
                    valueArray,
                  ]);
                } else {
                  filtersPlugin.addCondition(columnIndex, conditionName, args);
                }
              } catch (e) {
                console.error(
                  `Error applying condition to column ${columnIndex}:`,
                  e
                );
              }
            }
          }
        });

        // Apply the filters
        filtersPlugin.filter();
      }
    }

    return () => {
      if (hotRef.current) {
        hotRef.current.destroy();
        hotRef.current = null;
      }
    };
  }, [
    data,
    isLoading,
    selectedColumns,
    dateFilter,
    annotationsState,
    operationsState,
  ]);

  // Handle date filter changes
  const handleDateFilterChange = (
    startDate: string | null,
    endDate: string | null
  ) => {
    if (onDateFilterChange) {
      onDateFilterChange(startDate, endDate);
    }
  };

  // No custom filter modal needed as we're using Handsontable's built-in filtering

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Loading data..." />
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex justify-center items-center h-full">
        <p>No data available for table</p>
      </div>
    );
  }

  return (
    <div className="data-table-panel h-full flex flex-col">
      <div className="data-table-header p-2">
        <DateFilterPanel
          onDateFilterChange={handleDateFilterChange}
          dateFilter={dateFilter}
        />
      </div>
      <div
        ref={tableRef}
        className="flex-grow overflow-auto"
        style={{ height: onDateFilterChange ? "calc(100% - 50px)" : "100%" }}
      />
    </div>
  );
};

export default DataTablePanel;
