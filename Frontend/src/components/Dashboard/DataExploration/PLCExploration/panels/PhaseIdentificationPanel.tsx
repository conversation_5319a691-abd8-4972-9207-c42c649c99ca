import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Button, Spin, Empty } from 'antd';
import { SettingOutlined, BranchesOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import {
  PhaseIdentificationPanelProps
} from '../types/PLCTypes';

const PhaseIdentificationPanel: React.FC<PhaseIdentificationPanelProps> = ({
  configuration,
  isFullScreen = false,
  isLoading = false,
  hasError = false,
  errorMessage,
  onOpenConfiguration
}) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);
  const [chartUpdateTrigger, setChartUpdateTrigger] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const echartsRef = useRef<any>(null);
  const chartWrapperRef = useRef<HTMLDivElement>(null);

  const isConfigured = configuration &&
                      configuration.dataRange?.startDate &&
                      configuration.dataRange?.endDate &&
                      configuration.apiData;

  // Check if data is available
  const hasApiData = configuration?.apiData && Object.keys(configuration.apiData).length > 0;

  // Monitor container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setContainerSize({ width: offsetWidth, height: offsetHeight });
        setIsChartReady(offsetWidth > 0 && offsetHeight > 0);
      }
    };

    updateSize();
    
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Handle configuration button click
  const handleConfigureClick = () => {
    if (onOpenConfiguration) {
      onOpenConfiguration();
    }
  };

  // Force chart re-render when configuration changes
  useEffect(() => {
    // Clear the chart instance completely to prevent grid/axis misalignment
    if (echartsRef.current) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      if (chartInstance) {
        chartInstance.dispose();
      }
    }

    // Trigger re-render with optimized delay
    const timeoutId = setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [configuration?.basic?.selectedColumns?.headers?.join(','), configuration?.basic?.xAxisColumn, configuration?.apiData]);

  // Create plot data from API response
  const plotData = useMemo(() => {
    if (!hasApiData || !configuration?.apiData) {
      return [];
    }

    const apiData = configuration.apiData;

    // Handle different API response structures with proper TypeScript
    let seriesData: Array<{
      name?: string;
      data?: Array<[string, number]>;
      color?: string;
      lineStyle?: { width?: number; color?: string };
      symbolSize?: number;
    }> = [];

    // Check if data follows the same structure as batch comparison
    if (apiData.columnOptions) {
      // Handle columnOptions structure like: { Temperature: { series: [...] } }
      const columnNames = Object.keys(apiData.columnOptions);
      if (columnNames.length > 0) {
        const firstColumn = columnNames[0];
        const columnData = apiData.columnOptions[firstColumn];
        if (columnData?.series && Array.isArray(columnData.series)) {
          seriesData = columnData.series;
        }
      }
    } else if (apiData.series && Array.isArray(apiData.series)) {
      // Direct series data
      seriesData = apiData.series;
    } else if (apiData.data && Array.isArray(apiData.data)) {
      // Raw data array - create series structure
      const yAxis = configuration.basic?.selectedColumns?.headers?.[0] || 'Temperature';
      seriesData = [{
        name: `${yAxis} - ${apiData.batchId || 'Unknown'}`,
        data: apiData.data,
        color: '#52c41a'
      }];
    }

    if (!Array.isArray(seriesData) || seriesData.length === 0) {
      return [];
    }

    // Convert to plot data format with proper typing
    const result: Array<{
      name: string;
      data: Array<[string, number]>;
      groupValue: string | null;
      originalColumn: string;
      lineStyle: { width: number; color: string };
      symbolSize: number;
      color: string;
      seriesIndex: number;
      columnName: string;
      batchId: string;
      batchStartTime: number;
      batchEndTime: number;
      system: string;
      yAxis: string;
    }> = [];
    
    seriesData.forEach((series, seriesIndex) => {
      if (series?.data && Array.isArray(series.data) && series.data.length > 0) {
        const seriesName = series.name || `Phase ${seriesIndex + 1}`;

        // Determine color and width safely
        const color = series.color || series.lineStyle?.color || '#52c41a';
        const width = typeof series.lineStyle?.width === 'number' ? series.lineStyle.width : 2;

        // Calculate start and end times for this series with type safety
        const timestamps = series.data.map((point) => {
          if (Array.isArray(point) && point.length >= 2) {
            return new Date(point[0]).getTime();
          }
          return Date.now(); // Fallback
        });
        const startTime = Math.min(...timestamps);
        const endTime = Math.max(...timestamps);

        result.push({
          name: seriesName,
          data: series.data,
          groupValue: series.name || null,
          originalColumn: configuration.basic?.selectedColumns?.headers?.[0] || 'Temperature',
          lineStyle: { width, color },
          symbolSize: series.symbolSize || 4,
          color,
          seriesIndex: seriesIndex,
          columnName: configuration.basic?.selectedColumns?.headers?.[0] || 'Temperature',
          batchId: apiData.batchId || `Batch_${seriesIndex + 1}`,
          batchStartTime: startTime,
          batchEndTime: endTime,
          system: apiData.system || '',
          yAxis: apiData.yAxis || ''
        });
      }
    });

    return result;
  }, [hasApiData, configuration?.apiData, configuration?.basic?.selectedColumns?.headers]);

  // Create ECharts option for phase identification
  const createPhaseIdentificationOption = useMemo(() => {
    if (!plotData || plotData.length === 0) {
      return {
        series: [],
        xAxis: { type: 'time' },
        yAxis: { type: 'value' },
        hasData: false
      };
    }

    const xAxisColumn = configuration?.basic?.xAxisColumn || 'DateTime';
    const yAxisColumn = configuration?.basic?.selectedColumns?.headers?.[0] || 'Temperature';

    // Calculate Y-axis range
    let min = Infinity;
    let max = -Infinity;

    plotData.forEach((series: any) => {
      if (series.data && Array.isArray(series.data)) {
        series.data.forEach((point: any) => {
          if (point && Array.isArray(point) && typeof point[1] === 'number') {
            min = Math.min(min, point[1]);
            max = Math.max(max, point[1]);
          }
        });
      }
    });

    const yAxisRange = min !== Infinity && max !== -Infinity ? {
      min: min - (max - min) * 0.05,
      max: max + (max - min) * 0.05
    } : null;

    const chartOption = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' },
        appendToBody: true,
        className: 'phase-identification-tooltip',
        extraCssText: 'z-index: 9999; position: fixed;',
        formatter: function(params: any) {
          if (!params || params.length === 0) return '';

          const param = params[0];
          const phaseName = param.seriesName || 'Unknown Phase';
          const batchInfo = plotData[0]; // Since phase identification usually shows one batch

          return `
            <div style="
              background: linear-gradient(135deg, #52c41a15, #52c41a05);
              border: 1px solid #52c41a40;
              border-radius: 8px;
              padding: 12px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            ">
              <div style="
                font-weight: 600;
                font-size: 14px;
                color: #52c41a;
                margin-bottom: 8px;
                display: flex;
                align-items: center;
                gap: 6px;
              ">
                <span style="color: #333;">Phase:</span> ${phaseName}
              </div>

             <div style="
               display: grid;
               grid-template-columns: auto 1fr;
               gap: 4px 8px;
               font-size: 12px;
               color: #666;
               margin-bottom: 8px;
             ">
               <span style="font-weight: 500;">📊 Param:</span>
               <span>${batchInfo.yAxis || yAxisColumn}</span>
               <span style="font-weight: 500;">⏰ Time:</span>
               <span>${new Date(param.value[0]).toLocaleString()}</span>
             </div>

             <div style="
               border-top: 1px solid #f0f0f0;
               padding-top: 8px;
               font-size: 12px;
             ">
               <div style="
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
                 padding: 2px 0;
               ">
                 <span style="
                   display: flex;
                   align-items: center;
                   gap: 6px;
                   color: #333;
                   font-weight: 500;
                 ">
                   <span style="
                     display: inline-block;
                     width: 8px;
                     height: 8px;
                     border-radius: 50%;
                     background: ${param.color};
                   "></span>
                   ${yAxisColumn}
                 </span>
                 <span style="
                   font-weight: 600;
                   color: #1890ff;
                 ">
                   ${Array.isArray(param.value) ? param.value[1]?.toFixed(2) : param.value?.toFixed(2)}
                 </span>
               </div>
             </div>
           </div>
         `;
        }
      },
      legend: {
        data: plotData.map(s => s.name),
        top: isFullScreen ? '0%' : 10,
        type: 'scroll',
        itemGap: 8,
        textStyle: { fontSize: 11 }
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0],
          yAxisIndex: [],
          filterMode: 'filter'
        },
        {
          type: 'slider',
          xAxisIndex: [0],
          yAxisIndex: [],
          filterMode: 'filter',
          bottom: 20
        }
      ],
      grid: {
        left: isFullScreen ? '3%' : '60px',
        right: isFullScreen ? '3%' : '40px',
        top: isFullScreen ? '8%' : '15%',
        bottom: isFullScreen ? '15%' : '20%',
        containLabel: true
      },
      xAxis: {
        type: 'time',
        name: xAxisColumn,
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1,
            type: 'solid'
          }
        },
        axisLine: {
          show: true,
          lineStyle: { color: '#666' }
        },
        axisTick: { show: true },
        axisLabel: {
          show: true,
          fontSize: 11,
          color: '#666'
        }
      },
      yAxis: {
        type: 'value',
        name: yAxisColumn,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        scale: false,
        min: yAxisRange?.min,
        max: yAxisRange?.max,
        axisLabel: {
          formatter: function (value: any) {
            return typeof value === 'number' ? value.toFixed(2) : value;
          }
        }
      },
      series: plotData.map((s, seriesIndex) => ({
        name: s.name,
        type: 'line',
        data: s.data || [],
        smooth: false,
        symbol: 'circle',
        symbolSize: s.symbolSize || 4,
        lineStyle: {
          width: s.lineStyle.width,
          color: s.lineStyle.color
        },
        itemStyle: {
          color: s.color || s.lineStyle.color || '#1f77b4'
        },
        emphasis: {
          focus: 'series'
        }
      })),
      hasData: true
    };

    return chartOption;
  }, [plotData, isFullScreen, configuration?.basic?.xAxisColumn, configuration?.basic?.selectedColumns?.headers]);

  const handleDownloadPDF = async () => {
    if (!chartWrapperRef.current) return;
    const canvas = await html2canvas(chartWrapperRef.current, { useCORS: true });
    const imgData = canvas.toDataURL("image/png");
    const pdf = new jsPDF({
      orientation: "landscape",
      unit: "px",
      format: [canvas.width, canvas.height],
    });
    pdf.addImage(imgData, "PNG", 0, 0, canvas.width, canvas.height);
    pdf.save("phase-identification-chart.pdf");
  };

  // Show error state if there's an error (check this BEFORE configuration check)
  if (hasError) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<BranchesOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-red-600 mb-2">
                Error Loading Phase Identification Data
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {errorMessage || 'Failed to load phase identification data. Please try again.'}
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={() => onOpenConfiguration && onOpenConfiguration()}
              >
                Retry Configuration
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // Render empty state with configuration prompt when panel is not configured
  if (!configuration || !isConfigured) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full w-full"
        style={{ minHeight: '300px', width: '100%' }}
      >
        <Empty
          image={<BranchesOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-gray-700 mb-2">
                {!configuration ? 'Phase Identification Panel Not Configured' : 'Configuration Incomplete'}
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {!configuration
                  ? 'Configure this panel to identify and analyze process phases in your PLC data. First ensure you have batch comparison data loaded.'
                  : 'Please complete the configuration by selecting a batch and phase parameters'
                }
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
                style={{
                  background: '#52c41a',
                  borderColor: '#52c41a',
                  boxShadow: '0 2px 4px rgba(82, 196, 26, 0.2)'
                }}
              >
                Configure Panel
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // Show error state if API data has error
  if (hasApiData && configuration?.apiData?.error) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<BranchesOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-red-600 mb-2">
                Phase Identification Error
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {configuration.apiData.error}
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
                style={{
                  background: '#52c41a',
                  borderColor: '#52c41a'
                }}
              >
                Reconfigure Panel
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // Show empty state if no plot data
  if (isConfigured && plotData.length === 0) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<BranchesOutlined style={{ fontSize: '48px', color: '#52c41a' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-gray-800 mb-2">
                No Phase Data Available
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                No phase identification data found for the selected batch. Try selecting a different batch or check the parameters.
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
                style={{
                  background: '#52c41a',
                  borderColor: '#52c41a'
                }}
              >
                Adjust Parameters
              </Button>
            </div>
          }
        />
      </div>
    );
  }



  const xAxisColumn = configuration?.basic?.xAxisColumn || 'DateTime';
  const yAxisColumn = configuration?.basic?.selectedColumns?.headers?.[0] || 'Temperature';
  const batchId = configuration?.apiData?.batchId || 'Unknown';
  const system = configuration?.apiData?.system || 'Unknown';

  return (
    <div className={`phase-identification-panel ${isFullScreen ? 'h-full' : 'h-[95%]'}`} ref={containerRef} style={{ position: 'relative' }}>
      <div className={`flex justify-between items-center ${isFullScreen ? 'mb-0 px-2 pt-0' : 'mb-1 px-3 pt-1'}`}>
        <h3 className="text-base font-medium" style={{ color: '#222' }}>
          {yAxisColumn} (Batch: {batchId})
        </h3>
        <div className="flex gap-2">
          <Button onClick={handleDownloadPDF} icon={<BranchesOutlined />}>
            Download PDF
          </Button>
        </div>
      </div>
      <div className={`${isFullScreen ? 'p-0 h-[calc(100%-40px)]' : 'p-2 h-[calc(100%-50px)]'} overflow-auto`}>
        <div
          ref={chartWrapperRef}
          style={{
            height: '100%',
            minHeight: isFullScreen ? '100%' : '400px'
          }}
        >
          {containerSize.width > 0 && containerSize.height > 0 && isChartReady && !isLoading ? (
            <ReactECharts
              key={`phase-identification-${chartUpdateTrigger}`}
              ref={echartsRef}
              option={createPhaseIdentificationOption}
              style={{
                width: '100%',
                height: '100%',
                ...(isFullScreen ? {} : { minHeight: '400px' })
              }}
              opts={{ renderer: 'canvas' }}
              notMerge={true}
            />
          ) : (
            <div style={{
              width: '100%',
              height: isFullScreen ? '100%' : '400px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Spin size="large" tip="Loading phase identification chart..." />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PhaseIdentificationPanel; 