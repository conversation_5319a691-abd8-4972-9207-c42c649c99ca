// PLC-specific types and interfaces for PLC Data Exploration
import { ColumnSelection } from '../../types';

// PLC Component types that can be added to the grid
export enum PLCComponentType {
  PLCTimeSeriesPanel = 'PLCTimeSeriesPanel',
  // Future PLC panel types can be added here
}

// PLC Grid item data structure
export interface PLCGridItemData {
  id: string;
  type: PLCComponentType;
  title: string;
  config?: PLCPanelConfiguration;
  isConfigured?: boolean; // Track if panel has been configured
}

// PLC Panel Configuration interface
export interface PLCPanelConfiguration {
  // Data Range section
  dataRange?: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
  };

  // Basic section
  basic?: {
    xAxisColumn: string; // X-axis column name (DateTime, VatPH, etc.)
    selectedColumns?: ColumnSelection; // Use same structure as batch exploration (Y-axis columns)
    group: string | null; // None, Group by X, etc.
  };

  // Advanced section
  advanced?: {
    windowMode: boolean;
    windowConfig?: {
      enabled: boolean;
      groupColumn: string;
      // Support both old and new window config structures
      target?: string[]; // Legacy support
      pre?: number; // Legacy support
      post?: number; // Legacy support
      target_batch_ids?: string[]; // New structure
      pre_batch_ids?: string[]; // New structure
      post_batch_ids?: string[]; // New structure
    };
  };

  // Additional metadata
  title?: string;
  panelType?: PLCComponentType;
  lastModified?: string;

  // API data storage
  apiData?: {
    columnOptions?: Record<string, any>;
    columns?: string[]; // Available columns from batch-comparison API
    selectedBatches?: string[]; // Selected batch IDs
    availableBatches?: string[]; // Available batch IDs from date range
    qualityBatchIds?: Array<{
      batch_id: string;
      batch_quality: number;
      is_batch_good: boolean;
    }>; // Enhanced batch information with quality data
    compareLimit?: number; // Maximum batches allowed
    metadata?: {
      columnNames: string[];
      hasData: boolean;
      totalColumns: number;
      processedColumns: number;
      dateColumn?: string;
      error?: string;
    };
    // Chart data from batch-comparison API
    chartData?: any;
    groupedData?: Record<string, any>;
  };
}

// PLC Data source interface
export interface PLCDataSource {
  id: string;
  name: string;
  type: 'realtime' | 'historical';
  connectionStatus: 'connected' | 'disconnected' | 'error';
  lastUpdate?: string;
  features: PLCFeature[];
}

// PLC Feature interface (columns/variables available)
export interface PLCFeature {
  id: string;
  name: string;
  type: 'numeric' | 'boolean' | 'string' | 'datetime';
  unit?: string;
  description?: string;
}

// PLC Time series data interface
export interface PLCTimeSeriesData {
  timestamps: string[];
  values: number[];
  feature: PLCFeature;
  dataSource: string;
}

// PLC Panel state interface
export interface PLCPanelState {
  activePanels: PLCGridItemData[];
  drawerOpen: boolean;
  selectedPanelId: string | null;
  configurations: Record<string, PLCPanelConfiguration>;
}

// PLC Grid layout item (extends react-grid-layout)
export interface PLCLayoutItem {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
  static?: boolean;
  isDraggable?: boolean;
  isResizable?: boolean;
}

// PLC Layout array
export type PLCLayout = PLCLayoutItem[];

// PLC Filter types (simplified for current use)
export interface PLCFilter {
  id: string;
  type: 'range' | 'value' | 'time';
  feature: string;
  condition: 'equals' | 'greater' | 'less' | 'between' | 'contains';
  value: any;
  enabled: boolean;
}

// PLC API interfaces (optimized)
export interface PLCApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PLCDataRequest {
  dataSourceId: string;
  features: string[];
  timeRange: {
    start: string;
    end: string;
  };
  filters?: PLCFilter[];
}

// PLC Panel props interfaces
export interface PLCTimeSeriesPanelProps {
  configuration?: PLCPanelConfiguration;
  selectedColumns?: ColumnSelection;
  isFullScreen?: boolean;
  isEmpty?: boolean;
  onConfigurationChange?: (config: PLCPanelConfiguration) => void;
  onOpenConfiguration?: (currentlyDisplayedColumns?: string[]) => void;
}

export interface PLCGridItemProps {
  data: PLCGridItemData;
  layout?: PLCLayoutItem;
  onRemove?: (id: string) => void;
  onOpenConfiguration?: (panelId: string, currentlyDisplayedColumns?: string[]) => void;
  onConfigurationChange?: (panelId: string, config: PLCPanelConfiguration) => void;
}

export interface PLCGridLayoutProps {
  items: PLCGridItemData[];
  layout: PLCLayout;
  onLayoutChange: (layout: PLCLayout) => void;
  onItemsChange: (items: PLCGridItemData[]) => void;
  onDrop: (componentType: PLCComponentType, x: number, y: number) => void;
  onPanelRemove: (panelId: string) => void;
  onOpenConfiguration: (panelId: string, currentlyDisplayedColumns?: string[]) => void;
}

export interface PLCPanelConfigDrawerProps {
  open: boolean;
  onClose: () => void;
  panelId: string | null;
  configuration?: PLCPanelConfiguration;
  availableFeatures: PLCFeature[];
  onConfigurationSave: (panelId: string, config: PLCPanelConfiguration) => void;
}

export interface PLCSidebarProps {
  onBackClick?: () => void;
  activePanels: PLCComponentType[];
  availableDataSources: PLCDataSource[];
  onDataSourceSelect?: (dataSource: PLCDataSource) => void;
}

export interface PLCContentProps {
  selectedDataSource?: PLCDataSource;
  onDataSourceChange?: (dataSource: PLCDataSource | null) => void;
  activePanels?: PLCComponentType[];
  onPanelsChange?: (activePanels: PLCComponentType[]) => void;
}

// Default configurations
export const DEFAULT_PLC_PANEL_CONFIG: PLCPanelConfiguration = {
  dataRange: {
    startDate: null,
    endDate: null,
    startTime: null,
    endTime: null,
  },
  basic: {
    xAxisColumn: 'DateTime',
    selectedColumns: { indices: [], headers: [] },
    group: 'None',
  },
  advanced: {
    windowMode: false,
  },
};

// Panel size constants
export const PLC_PANEL_SIZES = {
  DEFAULT: { w: 6, h: 6 },
  MIN: { w: 3, h: 4 },
  MAX: { w: 12, h: 12 },
} as const;

// Grid configuration
export const PLC_GRID_CONFIG = {
  COLS: 12,
  ROW_HEIGHT: 60,
  MARGIN: [10, 10] as [number, number],
  CONTAINER_PADDING: [10, 10] as [number, number],
};
